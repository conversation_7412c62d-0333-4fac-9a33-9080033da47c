// DODO was here
// DODO created 54145210
import { useState, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { t, styled } from '@superset-ui/core';
import { Input } from 'src/components/Input';
import { RootState, DashboardLayout, LayoutItem } from 'src/dashboard/types';
import { CHART_TYPE } from 'src/dashboard/util/componentTypes';
import CopyToClipboard from 'src/components/CopyToClipboard';
import Icons from 'src/components/Icons';
import InfoTooltip from 'src/components/InfoTooltip';
import { Tooltip } from 'src/components/Tooltip';

interface ChartInfo {
  sliceId: number;
  sliceName: string;
  sliceNameOverride?: string;
  sliceNameOverrideRU?: string;
}

const StyledContainer = styled.div`
  ${({ theme }) => `
    padding-inline: ${theme.gridUnit * 4}px;
    padding-bottom: ${theme.gridUnit * 4}px;
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: ${theme.gridUnit * 4}px;
    
    .slice-id {
      font-weight: ${theme.typography.weights.bold};
      color: ${theme.colors.primary.base};
      user-select: text;
    }
    
    .slice-override {
      color: ${theme.colors.grayscale.base};
      font-style: italic;
    }

    .chart-list-cards-wrapper {
      overflow-y: auto;
      max-height: 100%;
    }

    .chart-list-cards {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: ${theme.gridUnit * 2}px;
      font-size: ${theme.typography.sizes.s}px;
    }
    
    .card {
      border: 1px solid ${theme.colors.grayscale.light2};
      border-radius: ${theme.borderRadius}px;
      overflow: hidden;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: ${theme.gridUnit}px ${theme.gridUnit * 2}px;
      border-bottom: 1px solid ${theme.colors.grayscale.light2};

      div {
        display: flex;
        align-items: center;
        gap: ${theme.gridUnit}px;
      }
    }
    
    .card-prop {
      padding: ${theme.gridUnit}px ${theme.gridUnit * 2}px;
      display: flex;
      align-items: center;
      gap: ${theme.gridUnit * 4}px;
    }

    .card-prop-name {
      color: ${theme.colors.grayscale.dark1};
      font-weight: ${theme.typography.weights.bold};
      white-space: nowrap;
    }
    
    .card-value {
      color: ${theme.colors.grayscale.base};
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  `}
`;

const ChartList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const dashboardLayout = useSelector<RootState, DashboardLayout>(
    state => state.dashboardLayout.present,
  );

  const sliceEntities = useSelector<RootState, any>(
    state => state.sliceEntities.slices,
  );

  const chartInfos = useMemo(() => {
    const charts: ChartInfo[] = [];

    Object.values(dashboardLayout).forEach((component: LayoutItem) => {
      if (component.type === CHART_TYPE && component.meta?.chartId) {
        const sliceId = component.meta.chartId;
        const slice = sliceEntities[sliceId];

        if (slice) {
          const { sliceNameOverride, sliceNameOverrideRU } = component.meta;

          charts.push({
            sliceId,
            sliceName: slice.slice_name || '',
            sliceNameOverride,
            sliceNameOverrideRU,
          });
        }
      }
    });

    return charts.sort((a, b) => a.sliceId - b.sliceId);
  }, [dashboardLayout, sliceEntities]);

  const filteredCharts = useMemo(() => {
    if (!searchTerm) return chartInfos;

    const lowerSearchTerm = searchTerm.toLowerCase();
    return chartInfos.filter(
      chart =>
        chart.sliceName.toLowerCase().includes(lowerSearchTerm) ||
        chart.sliceNameOverride?.toLowerCase().includes(lowerSearchTerm) ||
        chart.sliceNameOverrideRU?.toLowerCase().includes(lowerSearchTerm) ||
        chart.sliceId.toString().includes(lowerSearchTerm),
    );
  }, [chartInfos, searchTerm]);

  const renderCard = (chart: ChartInfo) => (
    <div className="card" key={chart.sliceId}>
      <div className="card-header">
        <div>
          ID:
          <span className="slice-id">{chart.sliceId}</span>
          <CopyToClipboard
            copyNode={<Icons.CopyOutlined iconSize="s" />}
            text={`.dashboard-chart-id-${chart.sliceId}`}
            tooltipText={t('Copy selector')}
            shouldShowText={false}
            wrapped={false}
          />
        </div>
        <InfoTooltip
          tooltip={`${t('Original title')}: ${chart.sliceName}`}
          placement="topRight"
          iconStyle={{ fontSize: '20px' }}
        />
      </div>
      <div className="card-prop">
        <span className="card-prop-name">EN</span>
        <Tooltip title={chart.sliceNameOverride}>
          <span className="card-value">{chart.sliceNameOverride || '-'}</span>
        </Tooltip>
      </div>
      <div className="card-prop">
        <span className="card-prop-name">RU</span>
        <Tooltip title={chart.sliceNameOverrideRU}>
          <span className="card-value">{chart.sliceNameOverrideRU || '-'}</span>
        </Tooltip>
      </div>
    </div>
  );

  return (
    <StyledContainer>
      <Input
        className="chart-list-search"
        placeholder={t('Search charts by name or ID')}
        value={searchTerm}
        onChange={e => setSearchTerm(e.target.value)}
        allowClear
      />
      <div className="chart-list-cards-wrapper">
        <div className="chart-list-cards">{filteredCharts.map(renderCard)}</div>
      </div>
    </StyledContainer>
  );
};

export default ChartList;
