import { useState } from 'react';
import { t, css, SupersetTheme } from '@superset-ui/core';
import Tabs from 'src/components/Tabs';
import { CssEditor as AceCssEditor } from 'src/components/AsyncAceEditor';
import DashboardComponents from 'src/DodoExtensions/dashboard/components/DashboardComponents';
import ChartList from 'src/DodoExtensions/dashboard/components/ChartList';
import { useDispatch } from 'react-redux';
import { onChange, updateCss } from 'src/dashboard/actions/dashboardState';

const CssPane = () => {
  const [activeTab, setActiveTab] = useState('css-editor');
  const [cssValue, setCssValue] = useState('');
  const dispatch = useDispatch();

  const handleChange = (css: string) => {
    setCssValue(css);
    dispatch(onChange());
    dispatch(updateCss(css));
  };

  const applyComponentStyles = (css: string) => {
    handleChange(cssValue ? `${cssValue}\n\n${css}` : css);
    setActiveTab('css-editor');
  };

  return (
    <Tabs
      css={css`
        height: 100%;
      `}
      activeKey={activeTab}
      onChange={setActiveTab}
    >
      <Tabs.TabPane key="css-editor" tab={t('CSS Editor')}>
        <div
          css={(theme: SupersetTheme) => css`
            padding-inline: ${theme.gridUnit * 2}px;
          `}
        >
          <h5>{t('Live CSS editor')}</h5>
          <AceCssEditor
            className="css-editor"
            minLines={12}
            maxLines={30}
            onChange={handleChange}
            height="400px"
            width="100%"
            editorProps={{ $blockScrolling: true }}
            enableLiveAutocompletion
            value={cssValue}
          />
        </div>
      </Tabs.TabPane>
      <Tabs.TabPane key="charts" tab={t('Charts')}>
        <ChartList />
      </Tabs.TabPane>
      <Tabs.TabPane key="components" tab={t('Components')}>
        <DashboardComponents applyComponentStyles={applyComponentStyles} />
      </Tabs.TabPane>
    </Tabs>
  );
};

export default CssPane;
