import { InfoCircleOutlined } from '@ant-design/icons';
import { t, styled } from '@superset-ui/core';
import Collapse from 'src/components/Collapse';
import CopyToClipboard from 'src/components/CopyToClipboard';
import Icons from 'src/components/Icons';
import { EmptyStateSmall } from 'src/components/EmptyState';
import { dashboardElements } from './utils';

const StyledContainer = styled.div`
  ${({ theme }) => `
    overflow-y: auto;
    padding-inline: ${theme.gridUnit * 4}px;
    max-height: calc(100% - 20px);
  `}
`;

const StyledHeader = styled.div`
  ${({ theme }) => `
    margin-bottom: ${theme.gridUnit * 4}px;

    h3 {
      margin: 0 0 ${theme.gridUnit * 2}px 0;
      color: ${theme.colors.grayscale.dark2};
      font-size: ${theme.typography.sizes.l}px;
      font-weight: ${theme.typography.weights.bold};
    }

    p {
      margin: 0;
      color: ${theme.colors.grayscale.base};
      font-size: ${theme.typography.sizes.m}px;
      line-height: 1.5;
      &:nth-child(n+2) {
        margin-top: ${theme.gridUnit}px;
      }
    }
  `}
`;

const BoldText = styled.span`
  ${({ theme }) => `
    font-weight: ${theme.typography.weights.bold};
  `}
`;

const StyledCollapse = styled(Collapse)`
  ${({ theme }) => `
    .ant-collapse-item {
      margin-bottom: ${theme.gridUnit * 2}px;
      border: 1px solid ${theme.colors.grayscale.light2};
      border-radius: ${theme.borderRadius}px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .ant-collapse-header {
      background: ${theme.colors.grayscale.light3};
      border-radius: ${theme.borderRadius}px ${theme.borderRadius}px 0 0;

      .ant-collapse-arrow {
        color: ${theme.colors.primary.base};
      }
    }

    .ant-collapse-content {
      border-top: 1px solid ${theme.colors.grayscale.light2};
    }

    .ant-collapse-content-box {
      padding-inline: ${theme.gridUnit * 4}px;
    }
  `}
`;

const ComponentItem = styled.div`
  ${({ theme }) => `
    padding-block: ${theme.gridUnit * 4}px;
  `}
`;

const ComponentTitle = styled.h4`
  ${({ theme }) => `
    margin: 0 0 ${theme.gridUnit * 2}px 0;
    color: ${theme.colors.grayscale.dark2};
    font-size: ${theme.typography.sizes.m}px;
    font-weight: ${theme.typography.weights.medium};
    display: flex;
    align-items: center;
    gap: ${theme.gridUnit}px;
  `}
`;

const ElementDescription = styled.p`
  ${({ theme }) => `
    margin: 0 0 ${theme.gridUnit * 2}px 0;
    color: ${theme.colors.grayscale.base};
    font-size: ${theme.typography.sizes.s}px;
    line-height: 1.4;
  `}
`;

const SelectorBox = styled.div`
  ${({ theme }) => `
    display: flex;
    align-items: center;
    gap: ${theme.gridUnit * 2}px;
    margin-bottom: ${theme.gridUnit * 2}px;

    code {
      background: ${theme.colors.grayscale.light4};
      color: ${theme.colors.grayscale.base};
      padding: ${theme.gridUnit}px ${theme.gridUnit * 2}px;
      border: 1px solid ${theme.colors.grayscale.light2};
      border-radius: ${theme.borderRadius}px;
      font-size: ${theme.typography.sizes.s}px;
      word-break: break-all;
    }
  `}
`;

const ExampleBox = styled.div`
  ${({ theme }) => `
    margin-top: ${theme.gridUnit * 4}px;

    div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: ${theme.gridUnit * 2}px;
    }

    h5 {
      margin: 0 0 ${theme.gridUnit}px 0;
      color: ${theme.colors.grayscale.dark1};
      font-size: ${theme.typography.sizes.s}px;
      font-weight: ${theme.typography.weights.medium};
    }

    .code-container {
      position: relative;

      .copy-button-container {
        position: absolute;
        top: ${theme.gridUnit * 2}px;
        right: ${theme.gridUnit * 2}px;
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      &:hover .copy-button-container {
        opacity: 1;
      }
    }

    pre {
      margin: 0;
      padding: ${theme.gridUnit * 2}px;
      width: 100%;
      background: ${theme.colors.grayscale.light4};
      color: ${theme.colors.grayscale.base};
      border: 1px solid ${theme.colors.grayscale.light2};
      border-radius: ${theme.borderRadius}px;
      font-size: ${theme.typography.sizes.xs}px;
      line-height: 1.4;
      overflow-x: auto;
      white-space: pre-wrap;
    }
  `}
`;

export interface DashboardElement {
  id: string;
  title: string;
  description: string;
  selector: string;
  category: string;
  examples: Array<{
    title: string;
    code: string;
  }>;
}

const categories = [
  { key: 'layout', label: t('Layout') },
  { key: 'charts', label: t('Charts') },
  { key: 'filters', label: t('Filters') },
];

const DashboardComponents = ({
  applyComponentStyles,
}: {
  applyComponentStyles: (css: string) => void;
}) => {
  const renderElement = (element: DashboardElement) => (
    <ComponentItem key={element.id}>
      <ComponentTitle>
        <InfoCircleOutlined />
        {element.title}
      </ComponentTitle>
      <ElementDescription>{element.description}</ElementDescription>

      <SelectorBox>
        <code>{element.selector}</code>
        <CopyToClipboard
          copyNode={<Icons.CopyOutlined iconSize="m" />}
          text={element.selector}
          tooltipText={t('Copy selector')}
          shouldShowText={false}
        />
      </SelectorBox>

      {element.examples.map((example, index) => (
        <ExampleBox key={index}>
          <div>
            <h5>{example.title}</h5>
            <span
              role="button"
              aria-label={t('Apply')}
              tabIndex={0}
              onClick={() => applyComponentStyles(example.code)}
            >
              {t('Apply')}
            </span>
          </div>

          <div className="code-container">
            <pre>{example.code}</pre>
            <div className="copy-button-container">
              <CopyToClipboard
                copyNode={<Icons.CopyOutlined iconSize="m" />}
                text={example.code}
                tooltipText={t('Copy code')}
                shouldShowText={false}
              />
            </div>
          </div>
        </ExampleBox>
      ))}
    </ComponentItem>
  );

  return (
    <StyledContainer>
      <StyledHeader>
        <h3>{t('Dashboard Components Guide')}</h3>
        <p>
          {t(
            'Customize your dashboard appearance using CSS. Click on any selector to copy it, then use it in the CSS Editor tab.',
          )}
        </p>
        <p>
          {t('If style rules are not applied, add')}{' '}
          <BoldText>!important</BoldText>{' '}
          {t('to the end of the rule to override existing styles.')}
        </p>
        <p>
          <BoldText>{t('Example')}</BoldText>: color: red !important;
        </p>
      </StyledHeader>

      {dashboardElements.length > 0 ? (
        <StyledCollapse ghost>
          {categories.map(category => {
            const categoryElements = dashboardElements.filter(
              el => el.category === category.key,
            );

            if (categoryElements.length === 0) return null;

            return (
              <Collapse.Panel header={category.label} key={category.key}>
                <div>{categoryElements.map(renderElement)}</div>
              </Collapse.Panel>
            );
          })}
        </StyledCollapse>
      ) : (
        <EmptyStateSmall title={t('No components found')} image="empty.svg" />
      )}
    </StyledContainer>
  );
};

export default DashboardComponents;
