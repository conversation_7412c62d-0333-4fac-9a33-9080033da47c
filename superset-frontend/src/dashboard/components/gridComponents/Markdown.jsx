// DODO was here
import { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import cx from 'classnames';

import { css, styled, t, SafeMarkdown } from '@superset-ui/core';
import { bootstrapData } from 'src/preamble'; // DODO added 53165180
import { Logger, LOG_ACTIONS_RENDER_CHART } from 'src/logger/LogUtils';
import { MarkdownEditor } from 'src/components/AsyncAceEditor';

import DeleteComponentButton from 'src/dashboard/components/DeleteComponentButton';
import { Draggable } from 'src/dashboard/components/dnd/DragDroppable';
import HoverMenu from 'src/dashboard/components/menu/HoverMenu';
import ResizableContainer from 'src/dashboard/components/resizable/ResizableContainer';
import MarkdownModeDropdown from 'src/dashboard/components/menu/MarkdownModeDropdown';
import WithPopoverMenu from 'src/dashboard/components/menu/WithPopoverMenu';
import { componentShape } from 'src/dashboard/util/propShapes';
import { ROW_TYPE, COLUMN_TYPE } from 'src/dashboard/util/componentTypes';
import {
  GRID_MIN_COLUMN_COUNT,
  GRID_MIN_ROW_UNITS,
  GRID_BASE_UNIT,
} from 'src/dashboard/util/constants';

const locale = bootstrapData?.common?.locale || 'en'; // DODO added 53165180

const propTypes = {
  id: PropTypes.string.isRequired,
  parentId: PropTypes.string.isRequired,
  component: componentShape.isRequired,
  parentComponent: componentShape.isRequired,
  index: PropTypes.number.isRequired,
  depth: PropTypes.number.isRequired,
  editMode: PropTypes.bool.isRequired,

  // from redux
  logEvent: PropTypes.func.isRequired,
  addDangerToast: PropTypes.func.isRequired,
  undoLength: PropTypes.number.isRequired,
  redoLength: PropTypes.number.isRequired,

  // grid related
  availableColumnCount: PropTypes.number.isRequired,
  columnWidth: PropTypes.number.isRequired,
  onResizeStart: PropTypes.func.isRequired,
  onResize: PropTypes.func.isRequired,
  onResizeStop: PropTypes.func.isRequired,

  // dnd
  deleteComponent: PropTypes.func.isRequired,
  handleComponentDrop: PropTypes.func.isRequired,
  updateComponents: PropTypes.func.isRequired,

  // HTML sanitization
  htmlSanitization: PropTypes.bool,
  htmlSchemaOverrides: PropTypes.object,
};

const defaultProps = {};

const MARKDOWN_PLACE_HOLDER_EN = `# ✨Header 1
## ✨Header 2
### ✨Header 3

<br />

Click here to learn more about [markdown formatting](https://bit.ly/1dQOfRK)`;

// DODO added 53165180
const MARKDOWN_PLACE_HOLDER_RU = `# ✨Заголовок 1
## ✨Заголовок 2
### ✨Заголовок 3

<br />

Нажмите здесь, чтобы узнать больше о [форматировании markdown](https://bit.ly/1dQOfRK)`;

const MARKDOWN_ERROR_MESSAGE = t('This markdown component has an error.');

const MarkdownStyles = styled.div`
  ${({ theme }) => css`
    &.dashboard-markdown {
      overflow: hidden;

      h4,
      h5,
      h6 {
        font-weight: ${theme.typography.weights.normal};
      }

      h5 {
        color: ${theme.colors.grayscale.base};
      }

      h6 {
        font-size: ${theme.typography.sizes.s}px;
      }

      .dashboard-component-chart-holder {
        overflow-y: auto;
        overflow-x: hidden;
      }

      .dashboard--editing & {
        cursor: move;
      }
    }
  `}
`;

class Markdown extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      isFocused: false,
      // DODO changed 53165180
      markdownSource: this.getMarkdownSource(
        props.component.meta,
        `preview_${locale}`,
      ),
      editor: null,
      editorMode: `preview_${locale}`,
      undoLength: props.undoLength,
      redoLength: props.redoLength,
    };
    this.renderStartTime = Logger.getTimestamp();

    this.handleChangeFocus = this.handleChangeFocus.bind(this);
    this.handleChangeEditorMode = this.handleChangeEditorMode.bind(this);
    this.handleMarkdownChange = this.handleMarkdownChange.bind(this);
    this.handleDeleteComponent = this.handleDeleteComponent.bind(this);
    this.handleResizeStart = this.handleResizeStart.bind(this);
    this.setEditor = this.setEditor.bind(this);
    this.getMarkdownSource = this.getMarkdownSource.bind(this); // DODO added 53165180
    this.getMarkdownPlaceholder = this.getMarkdownPlaceholder.bind(this); // DODO added 53165180
  }

  componentDidMount() {
    this.props.logEvent(LOG_ACTIONS_RENDER_CHART, {
      viz_type: 'markdown',
      start_offset: this.renderStartTime,
      ts: new Date().getTime(),
      duration: Logger.getTimestamp() - this.renderStartTime,
    });
  }

  // DODO added start 53165180
  getMarkdownSource(meta, mode) {
    return Markdown.getMarkdownSource(meta, mode, this.props.editMode);
  }

  getMarkdownPlaceholder() {
    const isRussian = this.state.editorMode.endsWith('_ru');
    return isRussian ? MARKDOWN_PLACE_HOLDER_RU : MARKDOWN_PLACE_HOLDER_EN;
  }

  static getMarkdownSource(meta, mode, editMode) {
    if (editMode) {
      const isRussian = mode.endsWith('_ru');
      return isRussian ? meta.codeRu || '' : meta.code || '';
    }
    const isRussian = locale === 'ru';
    return isRussian
      ? meta.codeRu || meta.code || ''
      : meta.code || meta.codeRu || '';
  }
  // DODO added stop 53165180

  static getDerivedStateFromProps(nextProps, state) {
    const { hasError, editorMode, markdownSource, undoLength, redoLength } =
      state;
    const {
      component: nextComponent,
      undoLength: nextUndoLength,
      redoLength: nextRedoLength,
      editMode, // DODO added 53165180
    } = nextProps;
    // user click undo or redo ?
    if (nextUndoLength !== undoLength || nextRedoLength !== redoLength) {
      return {
        ...state,
        undoLength: nextUndoLength,
        redoLength: nextRedoLength,
        // DODO changed 53165180
        markdownSource: Markdown.getMarkdownSource(
          nextComponent.meta,
          editorMode,
          editMode,
        ),
        hasError: false,
      };
    }

    // DODO changed start 53165180
    const currentSource = Markdown.getMarkdownSource(
      nextComponent.meta,
      editorMode,
      editMode,
    );
    if (
      !hasError &&
      editorMode.startsWith('preview') &&
      currentSource !== markdownSource
    ) {
      return {
        ...state,
        markdownSource: currentSource,
      };
    }
    // DODO changed stop 53165180

    return state;
  }

  static getDerivedStateFromError() {
    return {
      hasError: true,
    };
  }

  componentDidUpdate(prevProps) {
    if (
      this.state.editor &&
      (prevProps.component.meta.width !== this.props.component.meta.width ||
        prevProps.columnWidth !== this.props.columnWidth)
    ) {
      this.state.editor.resize(true);
    }
    // pre-load AceEditor when entering edit mode
    if (this.props.editMode) {
      MarkdownEditor.preload();
    }
  }

  componentDidCatch() {
    if (this.state.editor && this.state.editorMode.startsWith('preview')) {
      this.props.addDangerToast(
        t(
          'This markdown component has an error. Please revert your recent changes.',
        ),
      );
    }
  }

  setEditor(editor) {
    editor.getSession().setUseWrapMode(true);
    this.setState({
      editor,
    });
  }

  handleChangeFocus(nextFocus) {
    const nextFocused = !!nextFocus;
    const nextEditMode = nextFocused ? `edit_${locale}` : `preview_${locale}`; // DODO changed 53165180
    this.setState(() => ({ isFocused: nextFocused }));
    this.handleChangeEditorMode(nextEditMode);
  }

  handleChangeEditorMode(mode) {
    // DODO added start 53165180
    const { component } = this.props;
    const currentMode = this.state.editorMode;

    if (currentMode.startsWith('edit')) {
      this.updateMarkdownContent();
    }

    const newMarkdownSource = this.getMarkdownSource(component.meta, mode);
    // DODO added stop 53165180

    const nextState = {
      ...this.state,
      editorMode: mode,
      markdownSource: newMarkdownSource, // DODO added 53165180
    };

    if (mode.startsWith('preview')) {
      nextState.hasError = false;
    }

    this.setState(nextState);
  }

  updateMarkdownContent() {
    const { updateComponents, component } = this.props;
    // DODO added start 53165180
    const { editorMode, markdownSource } = this.state;
    const isRussian = editorMode.endsWith('_ru');

    const currentSource = isRussian
      ? component.meta.codeRu || ''
      : component.meta.code || '';

    if (currentSource !== markdownSource) {
      const metaUpdate = isRussian
        ? { codeRu: markdownSource }
        : { code: markdownSource };
      // DODO added stop 53165180

      updateComponents({
        [component.id]: {
          ...component,
          meta: {
            ...component.meta,
            ...metaUpdate, // DODO changed 53165180
          },
        },
      });
    }
  }

  handleMarkdownChange(nextValue) {
    this.setState({
      markdownSource: nextValue,
    });
  }

  handleDeleteComponent() {
    const { deleteComponent, id, parentId } = this.props;
    deleteComponent(id, parentId);
  }

  handleResizeStart(e) {
    const { editorMode } = this.state;
    const { editMode, onResizeStart } = this.props;
    const isEditing = editorMode.startsWith('edit');
    onResizeStart(e);
    if (editMode && isEditing) {
      this.updateMarkdownContent();
    }
  }

  renderEditMode() {
    const { editorMode } = this.state; // DODO added 53165180
    const isRussian = editorMode.endsWith('_ru'); // DODO added 53165180

    return (
      <MarkdownEditor
        onChange={this.handleMarkdownChange}
        width="100%"
        height="100%"
        showGutter={false}
        editorProps={{ $blockScrolling: true }}
        value={
          // this allows "select all => delete" to give an empty editor
          typeof this.state.markdownSource === 'string'
            ? this.state.markdownSource
            : this.getMarkdownPlaceholder(isRussian) // DODO changed 53165180
        }
        readOnly={false}
        onLoad={this.setEditor}
        data-test="editor"
      />
    );
  }

  renderPreviewMode() {
    const { hasError, editorMode } = this.state;
    const { component } = this.props; // DODO added 53165180

    // DODO added 53165180
    const previewSource = this.getMarkdownSource(component.meta, editorMode);

    return (
      <SafeMarkdown
        source={
          hasError
            ? MARKDOWN_ERROR_MESSAGE
            : previewSource || this.getMarkdownPlaceholder() // DODO changed 53165180
        }
        htmlSanitization={this.props.htmlSanitization}
        htmlSchemaOverrides={this.props.htmlSchemaOverrides}
      />
    );
  }

  render() {
    const { isFocused, editorMode } = this.state;

    const {
      component,
      parentComponent,
      index,
      depth,
      availableColumnCount,
      columnWidth,
      onResize,
      onResizeStop,
      handleComponentDrop,
      editMode,
    } = this.props;

    // inherit the size of parent columns
    const widthMultiple =
      parentComponent.type === COLUMN_TYPE
        ? parentComponent.meta.width || GRID_MIN_COLUMN_COUNT
        : component.meta.width || GRID_MIN_COLUMN_COUNT;

    const isEditing = editorMode.startsWith('edit');

    return (
      <Draggable
        component={component}
        parentComponent={parentComponent}
        orientation={parentComponent.type === ROW_TYPE ? 'column' : 'row'}
        index={index}
        depth={depth}
        onDrop={handleComponentDrop}
        disableDragDrop={isFocused}
        editMode={editMode}
      >
        {({ dragSourceRef }) => (
          <WithPopoverMenu
            onChangeFocus={this.handleChangeFocus}
            menuItems={[
              <MarkdownModeDropdown
                id={`${component.id}-mode`}
                value={this.state.editorMode}
                onChange={this.handleChangeEditorMode}
              />,
            ]}
            editMode={editMode}
          >
            <MarkdownStyles
              data-test="dashboard-markdown-editor"
              className={cx(
                'dashboard-markdown',
                isEditing && 'dashboard-markdown--editing',
              )}
              id={component.id}
            >
              <ResizableContainer
                id={component.id}
                adjustableWidth={parentComponent.type === ROW_TYPE}
                adjustableHeight
                widthStep={columnWidth}
                widthMultiple={widthMultiple}
                heightStep={GRID_BASE_UNIT}
                heightMultiple={component.meta.height}
                minWidthMultiple={GRID_MIN_COLUMN_COUNT}
                minHeightMultiple={GRID_MIN_ROW_UNITS}
                maxWidthMultiple={availableColumnCount + widthMultiple}
                onResizeStart={this.handleResizeStart}
                onResize={onResize}
                onResizeStop={onResizeStop}
                editMode={isFocused ? false : editMode}
              >
                <div
                  ref={dragSourceRef}
                  className="dashboard-component dashboard-component-markdown dashboard-component-chart-holder "
                  data-test="dashboard-component-chart-holder"
                >
                  {editMode && (
                    <HoverMenu position="top">
                      <DeleteComponentButton
                        onDelete={this.handleDeleteComponent}
                      />
                    </HoverMenu>
                  )}
                  {editMode && isEditing
                    ? this.renderEditMode()
                    : this.renderPreviewMode()}
                </div>
              </ResizableContainer>
            </MarkdownStyles>
          </WithPopoverMenu>
        )}
      </Draggable>
    );
  }
}

Markdown.propTypes = propTypes;
Markdown.defaultProps = defaultProps;

function mapStateToProps(state) {
  return {
    undoLength: state.dashboardLayout.past.length,
    redoLength: state.dashboardLayout.future.length,
    htmlSanitization: state.common.conf.HTML_SANITIZATION,
    htmlSchemaOverrides: state.common.conf.HTML_SANITIZATION_SCHEMA_EXTENSIONS,
  };
}
export default connect(mapStateToProps)(Markdown);
